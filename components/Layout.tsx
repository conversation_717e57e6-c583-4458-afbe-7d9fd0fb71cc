import React, { useEffect, useRef, useState } from 'react';
import { Link, NavLink, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { themeSettings, useTheme } from '../contexts/ThemeContext';
import CategorySearch from './CategorySearch';
import { ChevronDownIcon, StarIcon as InstallIcon } from './icons'; // Using StarIcon as a placeholder for Install
import { IS_ELECTRON, isPWAEnvironment } from '../constants';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: Array<string>;
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

// iOS设备检测函数
const isIOS = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
};

// 检测是否在Safari中
const isSafari = (): boolean => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

// 检测是否已经安装为PWA
const isPWAInstalled = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true;
};

const ThemeSwitcher: React.FC = () => {
  const { themeName, setThemeName, theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const switcherRef = useRef<HTMLDivElement>(null);

  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [canInstallPWA, setCanInstallPWA] = useState(false);
  const [showIOSInstallPrompt, setShowIOSInstallPrompt] = useState(false);

  useEffect(() => {
    // 仅在PWA环境中启用安装提示
    if (!isPWAEnvironment()) {
      return;
    }

    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later.
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // Update UI notify the user they can add to home screen
      setCanInstallPWA(true);
    };

    // 检查当前环境
    const isIOSDevice = isIOS();
    const isInSafari = isSafari();
    const isAlreadyInstalled = isPWAInstalled();

    if (isAlreadyInstalled) {
      setCanInstallPWA(false);
      setShowIOSInstallPrompt(false);
    } else if (isIOSDevice && isInSafari) {
      // iOS Safari - 显示手动安装提示
      setShowIOSInstallPrompt(true);
      setCanInstallPWA(false);
    } else if (!isIOSDevice) {
      // 非iOS设备 - 监听beforeinstallprompt事件
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      return;
    }
    // Show the prompt
    deferredPrompt.prompt();
    // Wait for the user to respond to the prompt
    await deferredPrompt.userChoice;
    // We've used the prompt, and can't use it again, discard it
    setDeferredPrompt(null);
    setCanInstallPWA(false);
    setIsOpen(false); // Close dropdown after action
  };

  const handleIOSInstallClick = () => {
    // 显示iOS安装说明的模态框或提示
    alert(`要将此应用添加到主屏幕：\n\n1. 点击Safari底部的分享按钮 (📤)\n2. 滚动并选择"添加到主屏幕"\n3. 点击"添加"完成安装`);
    setIsOpen(false);
  };

  const availableThemes = Object.entries(themeSettings).map(([key, value]) => ({
    id: key as keyof typeof themeSettings,
    name: value.name,
  }));

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (switcherRef.current && !switcherRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div ref={switcherRef} className='h-10 flex items-center justify-center relative'>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center p-2 rounded-md ${theme.iconButton} ${theme.button.transition} hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-1 ${theme.input.focusRing.replace('focus:ring-2', '').trim()}`}
        aria-haspopup='true'
        aria-expanded={isOpen}
        aria-label='Select theme or install app'
      >
        <span className='sr-only'>Select Theme</span>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          fill='none'
          viewBox='0 0 24 24'
          strokeWidth={1.5}
          stroke='currentColor'
          className='w-5 h-5'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            d='M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.994 15.994 0 011.622-3.395m3.42 3.42a15.995 15.995 0 004.764-4.648l3.876-5.814a1.151 1.151 0 00-1.597-1.597L14.146 6.32a15.996 15.996 0 00-4.649 4.763m3.42 3.42a6.776 6.776 0 00-3.42-3.42'
          />
        </svg>
        <ChevronDownIcon
          className={`w-4 h-4 ml-1 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      {isOpen && (
        <div
          className={`absolute right-0 mt-2 w-48 sm:w-56 ${theme.dropdown.bg} ${theme.modal.rounded} ${theme.modal.shadow} z-50 py-1 animate-fadeInUp top-full`}
          style={{ animationDuration: '0.2s' }}
          role='menu'
          aria-orientation='vertical'
          aria-labelledby='theme-options-menu'
        >
          {canInstallPWA && (
            <button
              onClick={handleInstallClick}
              className={`flex items-center w-full text-left px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm transition-colors duration-150
                  ${theme.dropdown.itemText} ${theme.dropdown.itemHoverText} ${theme.dropdown.itemHoverBg}
                  border-b ${theme.input.border} 
                `}
              role='menuitem'
            >
              <InstallIcon className='w-4 h-4 mr-2' filled={false} />
              Install App
            </button>
          )}
          {showIOSInstallPrompt && (
            <button
              onClick={handleIOSInstallClick}
              className={`flex items-center w-full text-left px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm transition-colors duration-150
                  ${theme.dropdown.itemText} ${theme.dropdown.itemHoverText} ${theme.dropdown.itemHoverBg}
                  border-b ${theme.input.border} 
                `}
              role='menuitem'
            >
              <InstallIcon className='w-4 h-4 mr-2' filled={false} />
              添加到主屏幕
            </button>
          )}
          {availableThemes.map(t => (
            <button
              key={t.id}
              onClick={() => {
                setThemeName(t.id);
                setIsOpen(false);
              }}
              className={`block w-full text-left px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm transition-colors duration-150
                  ${
                    themeName === t.id
                      ? `${theme.dropdown.itemActiveText} ${theme.dropdown.itemActiveBg}`
                      : `${theme.dropdown.itemText} ${theme.dropdown.itemHoverText} ${theme.dropdown.itemHoverBg}`
                  }
                `}
              role='menuitem'
            >
              {t.name} {themeName === t.id && '✓'}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { theme } = useTheme();
  const location = useLocation();
  const { isAuthenticated, logout } = useAuth(); // Use AuthContext
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  // 显示导入模式选择对话框
  const showImportModeDialog = (categoryCount: number, imageCount: number): Promise<{ mode: 'overwrite' | 'append', overwriteExisting: boolean } | null> => {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      dialog.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 shadow-xl">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">选择导入模式</h3>
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
            发现：<strong>${categoryCount}</strong> 个分类，<strong>${imageCount}</strong> 张图片
          </p>

          <div class="space-y-3 mb-6">
            <label class="flex items-start space-x-3 cursor-pointer">
              <input type="radio" name="importMode" value="overwrite" checked class="mt-1">
              <div>
                <div class="font-medium text-gray-900 dark:text-white">覆盖模式</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">清空现有数据库，导入新数据</div>
                <div class="text-xs text-red-600 dark:text-red-400">⚠️ 将删除所有现有分类和图片</div>
              </div>
            </label>

            <label class="flex items-start space-x-3 cursor-pointer">
              <input type="radio" name="importMode" value="append" class="mt-1">
              <div>
                <div class="font-medium text-gray-900 dark:text-white">追加模式</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">保留现有数据，添加新分类和图片</div>
                <div class="text-xs text-blue-600 dark:text-blue-400">💡 现有数据将被保留</div>
              </div>
            </label>
          </div>

          <div id="appendOptions" class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded hidden">
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" id="overwriteExisting" class="rounded">
              <span class="text-sm text-gray-700 dark:text-gray-300">覆盖同名分类</span>
            </label>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              如果发现同名分类，是否覆盖现有分类
            </div>
          </div>

          <div class="flex space-x-3">
            <button id="cancelBtn" class="flex-1 px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700">
              取消
            </button>
            <button id="confirmBtn" class="flex-1 px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700">
              确定导入
            </button>
          </div>
        </div>
      `;

      document.body.appendChild(dialog);

      // 处理追加模式选项显示
      const appendRadio = dialog.querySelector('input[value="append"]') as HTMLInputElement;
      const overwriteRadio = dialog.querySelector('input[value="overwrite"]') as HTMLInputElement;
      const appendOptions = dialog.querySelector('#appendOptions') as HTMLElement;

      const updateAppendOptions = () => {
        if (appendRadio.checked) {
          appendOptions.classList.remove('hidden');
        } else {
          appendOptions.classList.add('hidden');
        }
      };

      appendRadio.addEventListener('change', updateAppendOptions);
      overwriteRadio.addEventListener('change', updateAppendOptions);

      // 处理按钮点击
      const cancelBtn = dialog.querySelector('#cancelBtn') as HTMLButtonElement;
      const confirmBtn = dialog.querySelector('#confirmBtn') as HTMLButtonElement;

      cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialog);
        resolve(null);
      });

      confirmBtn.addEventListener('click', () => {
        const selectedMode = dialog.querySelector('input[name="importMode"]:checked') as HTMLInputElement;
        const overwriteExisting = dialog.querySelector('#overwriteExisting') as HTMLInputElement;

        document.body.removeChild(dialog);
        resolve({
          mode: selectedMode.value as 'overwrite' | 'append',
          overwriteExisting: overwriteExisting.checked
        });
      });

      // ESC键取消
      const handleEsc = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          document.body.removeChild(dialog);
          document.removeEventListener('keydown', handleEsc);
          resolve(null);
        }
      };
      document.addEventListener('keydown', handleEsc);
    });
  };

  const handleFooterAction = () => {
    if (isAuthenticated) {
      logout();
    } else {
      // Navigation to login is handled by Link component directly
    }
  };

  const handleDatabaseReset = async () => {
    if (!IS_ELECTRON) {
      return;
    }

    // 显示确认对话框
    const confirmed = window.confirm(
      '确定要重置数据库吗？\n\n这将删除所有分类、图片和标签数据，并重新初始化为默认的三个分类（喜鹊、麻雀、斑鸠）。\n\n此操作不可撤销！'
    );

    if (!confirmed) {
      return;
    }

    setIsResetting(true);

    try {
      // 调用重置数据库的API
      const result = await (window as any).electronAPI.resetDatabase();
      
      if (result.success) {
        // 重置成功后刷新页面
        alert('数据库重置成功！页面将自动刷新。');
        window.location.reload();
      } else {
        alert(`数据库重置失败：${result.message}`);
      }
    } catch (error) {
      alert(`数据库重置失败：${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsResetting(false);
    }
  };

  const handleFolderImport = async () => {
    if (!IS_ELECTRON) {
      return;
    }

    setIsImporting(true);

    try {
      // 1. 显示文件夹选择对话框
      const folderResult = await (window as any).electronAPI.selectImportFolder();

      if (!folderResult || folderResult.canceled) {
        return; // 用户取消了选择
      }

      const folderPath = folderResult.filePath;
      if (!folderPath) {
        alert('未获取到有效的文件夹路径，请重试。');
        return;
      }

      // 2. 验证文件夹结构
      const validationResult = await (window as any).electronAPI.validateFolderStructure(folderPath);

      if (!validationResult.isValid) {
        alert(`文件夹结构不符合要求：\n\n${validationResult.errors.join('\n')}\n\n请确保文件夹结构为：\n- 根目录包含多个分类文件夹\n- 每个分类文件夹包含对应的图片文件\n- 支持的图片格式：.jpg, .jpeg, .png, .gif, .bmp, .webp`);
        return;
      }

      // 3. 显示导入模式选择对话框
      const categoryCount = validationResult.categories.length;
      const imageCount = validationResult.categories.reduce((sum: number, cat: any) => sum + cat.imageCount, 0);

      const importMode = await showImportModeDialog(categoryCount, imageCount);
      if (!importMode) {
        return; // 用户取消了导入
      }

      // 4. 开始导入（包含性能优化选项）
      const importOptions = {
        importMode: importMode.mode,
        overwriteExisting: importMode.overwriteExisting,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,

        // 性能优化选项
        maxConcurrentUploads: 3,           // 最大并发上传数
        batchSize: 50,                     // 分批处理大小
        enableProgressiveUpload: true,     // 启用渐进式上传
        memoryLimitMB: 100,               // 内存限制MB
      };

      // 设置进度监听
      const progressHandler = (progress: any) => {
        // 这里可以后续添加进度条显示
        console.log(`📊 导入进度: ${progress.progress}% - ${progress.stage}`, progress);
      };

      (window as any).electronAPI.onImportProgress(progressHandler);

      const result = await (window as any).electronAPI.importFromFolder(folderPath, importOptions);

      if (result.success) {
        const modeText = importMode.mode === 'overwrite' ? '覆盖模式' : '追加模式';
        alert(`导入成功！\n\n导入模式：${modeText}\n导入统计：\n- 分类：${result.importedCategories}/${result.totalCategories}\n- 图片：${result.importedImages}/${result.totalImages}\n- 失败：${result.failedImages}\n\n页面将自动刷新。`);
        window.location.reload();
      } else {
        alert(`导入失败：\n\n${result.errors.join('\n')}`);
      }
    } catch (error) {
      alert(`文件夹导入失败：${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsImporting(false);
    }
  };

  useEffect(() => {
    // 仅在PWA环境中请求通知权限
    if (!isPWAEnvironment()) {
      return;
    }

    // Optional: Request notification permission (example from PWA plan)
    // Consider when best to ask for this - perhaps after user interaction or specific event.
    if ('Notification' in window && 'serviceWorker' in navigator) {
      if (Notification.permission === 'default') {
        // Only ask if not already granted or denied
        Notification.requestPermission()
          .then(permission => {
            if (permission === 'granted') {
              // Optionally, show a gentle in-app notification or enable notification-related UI
              // Example:
              // navigator.serviceWorker.ready.then(registration => {
              //   registration.showNotification('Pokedex App Ready', {
              //     body: 'You can now receive updates!',
              //     icon: '/icons/icon-192x192.png'
              //   });
              // });
            }
          })
          .catch(() => {
            // Handle error silently
          });
      }
    }
  }, []);

  return (
    <div className={`min-h-screen flex flex-col ${theme.text} transition-colors duration-300`}>
      <header
        className={`${theme.headerBg} ${theme.headerText} shadow-md sticky top-0 z-40 transition-colors duration-300 h-12`}
      >
        <div className='container mx-auto px-2 sm:px-4 h-full flex justify-between items-center'>
          <Link
            to='/'
            className={`text-xl sm:text-2xl font-semibold ${theme.brandColor} transition-opacity hover:opacity-80 flex-shrink-0`}
          >
            Pokedex
          </Link>

          <nav className='flex items-center flex-shrink-0'>
            <div
              className={`flex items-center space-x-1 sm:space-x-1.5 md:space-x-2 transition-opacity duration-300 ${isSearchActive ? 'opacity-50 pointer-events-none' : 'opacity-100'}`}
            >
              <NavLink
                to='/'
                className={({ isActive }) =>
                  `${theme.navLink} ${isActive ? theme.navLinkActive : ''} px-1.5 py-1 sm:px-3 sm:py-2 rounded-md text-xs sm:text-sm whitespace-nowrap`
                }
              >
                Categories
              </NavLink>
              <NavLink
                to='/species'
                className={({ isActive }) =>
                  `${theme.navLink} ${isActive ? theme.navLinkActive : ''} px-1.5 py-1 sm:px-3 sm:py-2 rounded-md text-xs sm:text-sm whitespace-nowrap`
                }
              >
                Analytics
              </NavLink>

              <CategorySearch
                isExpanded={isSearchActive}
                onFocus={() => setIsSearchActive(true)}
                onBlur={() => setIsSearchActive(false)}
              />

              {IS_ELECTRON && (
                <button
                  onClick={handleDatabaseReset}
                  disabled={isResetting}
                  className={`${theme.navLink} px-1.5 py-1 sm:px-3 sm:py-2 rounded-md text-xs sm:text-sm whitespace-nowrap flex items-center gap-1 transition-colors duration-150 ${
                    isResetting ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  title="重置数据库"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className={`w-4 h-4 ${isResetting ? 'animate-spin' : ''}`}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m-4.991 4.99a8.25 8.25 0 0 1-2.51 1.709M6.75 12a5.25 5.25 0 1 1 10.5 0 5.25 5.25 0 0 1-10.5 0Z"
                    />
                  </svg>
                  <span className="hidden sm:inline">
                    {isResetting ? '重置中...' : '重置'}
                  </span>
                </button>
              )}

              {IS_ELECTRON && (
                <button
                  onClick={handleFolderImport}
                  disabled={isImporting}
                  className={`${theme.navLink} px-1.5 py-1 sm:px-3 sm:py-2 rounded-md text-xs sm:text-sm whitespace-nowrap flex items-center gap-1 transition-colors duration-150 ${
                    isImporting ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  title="从文件夹导入"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className={`w-4 h-4 ${isImporting ? 'animate-pulse' : ''}`}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                    />
                  </svg>
                  <span className="hidden sm:inline">
                    {isImporting ? '导入中...' : '导入'}
                  </span>
                </button>
              )}

              <ThemeSwitcher />
            </div>
          </nav>
        </div>
      </header>

      <main className='flex-grow container mx-auto px-3 sm:px-4 py-4 sm:py-8'>
        <div
          key={location.pathname}
          className='animate-fadeInUp'
          style={{ animationDuration: '0.4s' }}
        >
          {children}
        </div>
      </main>

      <footer
        className={`${theme.headerBg} text-center py-3 sm:py-4 border-t ${theme.name === 'Modern Clean Pro' ? 'border-slate-200 dark:border-slate-700/50' : theme.name === 'Neon Galaxy' ? 'border-cyan-500/20 dark:border-cyan-400/20' : theme.name === 'Arcade Flash' ? 'border-black dark:border-yellow-400' : 'border-gray-200 dark:border-gray-700'} transition-colors duration-300`}
      >
        {/* Electron模式下始终显示已认证状态的footer，Web模式下根据认证状态显示 */}
        {(IS_ELECTRON || isAuthenticated) ? (
          <button
            onClick={IS_ELECTRON ? undefined : handleFooterAction}
            className={`text-xs sm:text-sm ${theme.footerText} ${IS_ELECTRON ? '' : 'hover:underline'} ${theme.brandColor} ${IS_ELECTRON ? 'cursor-default' : 'focus:outline-none'}`}
            aria-label={IS_ELECTRON ? 'Local Mode' : 'Logout'}
            disabled={IS_ELECTRON}
          >
            &copy; 2025 Made With{' '}
            <span className={`${theme.footerHeartColor} transition-colors duration-300`}>❤</span>{' '}
            By 黄不盈. All rights reserved. {IS_ELECTRON && '(Local Mode)'}
          </button>
        ) : (
          <Link
            to='/login'
            onClick={handleFooterAction} // Still call, though it does nothing if not authenticated
            className={`text-xs sm:text-sm ${theme.footerText} hover:underline ${theme.brandColor}`}
            aria-label='Login or Register'
          >
            &copy; 2025 Made With{' '}
            <span className={`${theme.footerHeartColor} transition-colors duration-300`}>❤</span>{' '}
            By 黄不盈. All rights reserved.
          </Link>
        )}
      </footer>
    </div>
  );
};

export default Layout;
