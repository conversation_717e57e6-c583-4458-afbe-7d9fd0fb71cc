import { z } from 'zod';
import { NonEmptyStringSchema } from './common';

/**
 * 物种字典相关的 Schema 定义
 * 专门用于物种列表.json数据的类型验证和处理
 */

// 物种字典条目Schema（匹配JSON文件格式）
export const SpeciesDictionaryEntrySchema = z.object({
  目: NonEmptyStringSchema,  // Order - 目
  科: NonEmptyStringSchema,  // Family - 科  
  属: NonEmptyStringSchema,  // Genus - 属
  种: NonEmptyStringSchema,  // Species - 种
});

// 搜索查询选项Schema
export const SpeciesDictionarySearchOptionsSchema = z.object({
  query: NonEmptyStringSchema,
  limit: z.number().int().min(1).max(100).default(20),
  includeFamily: z.boolean().default(false),
  includeGenus: z.boolean().default(false),
});

// 搜索结果Schema
export const SpeciesDictionarySearchResultSchema = z.object({
  species: z.array(SpeciesDictionaryEntrySchema),
  total: z.number().int().min(0),
  query: NonEmptyStringSchema,
  searchTime: z.number().min(0), // 搜索耗时（毫秒）
});

// 按科属查询选项Schema
export const SpeciesByGroupOptionsSchema = z.object({
  groupName: NonEmptyStringSchema, // 科名或属名
  limit: z.number().int().min(1).max(500).default(50),
});

/**
 * 类型导出
 */
export type SpeciesDictionaryEntry = z.infer<typeof SpeciesDictionaryEntrySchema>;
export type SpeciesDictionarySearchOptions = z.infer<typeof SpeciesDictionarySearchOptionsSchema>;
export type SpeciesDictionarySearchResult = z.infer<typeof SpeciesDictionarySearchResultSchema>;
export type SpeciesByGroupOptions = z.infer<typeof SpeciesByGroupOptionsSchema>;

/**
 * 验证函数
 */

// 验证物种字典条目
export function validateSpeciesDictionaryEntry(data: unknown): SpeciesDictionaryEntry {
  return SpeciesDictionaryEntrySchema.parse(data);
}

// 验证搜索选项
export function validateSpeciesDictionarySearchOptions(data: unknown): SpeciesDictionarySearchOptions {
  return SpeciesDictionarySearchOptionsSchema.parse(data);
}

// 验证搜索结果
export function validateSpeciesDictionarySearchResult(data: unknown): SpeciesDictionarySearchResult {
  return SpeciesDictionarySearchResultSchema.parse(data);
}

// 验证科属查询选项
export function validateSpeciesByGroupOptions(data: unknown): SpeciesByGroupOptions {
  return SpeciesByGroupOptionsSchema.parse(data);
}
