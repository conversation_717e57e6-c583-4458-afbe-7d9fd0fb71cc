import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { app, BrowserWindow, ipcMain } from 'electron';
import path from 'path';
import fs from 'fs/promises';
import { SpeciesService } from '../../../electron/services/SpeciesService';
import type { SpeciesDictionarySearchOptions } from '../../../schemas/speciesDictionary';

// 模拟Electron环境
vi.mock('electron', () => ({
  app: {
    whenReady: vi.fn().mockResolvedValue(undefined),
    quit: vi.fn(),
    on: vi.fn(),
  },
  BrowserWindow: vi.fn().mockImplementation(() => ({
    loadURL: vi.fn(),
    webContents: {
      send: vi.fn(),
      on: vi.fn(),
    },
    on: vi.fn(),
    show: vi.fn(),
  })),
  ipcMain: {
    handle: vi.fn(),
    removeHandler: vi.fn(),
  },
}));

// 测试数据路径
const TEST_DATA_PATH = path.join(__dirname, '../../fixtures/integration-species.json');

// 完整的测试物种数据（模拟真实数据）
const integrationSpeciesData = [
  { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "白头鹎" },
  { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "黑头鹎" },
  { "目": "雀形目", "科": "鸦科", "属": "鹎属", "种": "红头鹎" },
  { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "家麻雀" },
  { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "山麻雀" },
  { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "树麻雀" },
  { "目": "鹰形目", "科": "鹰科", "属": "鹰属", "种": "苍鹰" },
  { "目": "鹰形目", "科": "鹰科", "属": "鹰属", "种": "雀鹰" },
  { "目": "鹰形目", "科": "隼科", "属": "隼属", "种": "游隼" },
  { "目": "鸽形目", "科": "鸠鸽科", "属": "鸽属", "种": "岩鸽" },
];

describe('Species Integration Tests', () => {
  let speciesService: SpeciesService;
  let ipcHandlers: Map<string, Function>;
  let mockWindow: any;

  beforeAll(async () => {
    // 创建测试数据文件
    await fs.writeFile(TEST_DATA_PATH, JSON.stringify(integrationSpeciesData, null, 2));
    
    // 初始化模拟的IPC处理器存储
    ipcHandlers = new Map();
    
    // 模拟ipcMain.handle
    (ipcMain.handle as any).mockImplementation((channel: string, handler: Function) => {
      ipcHandlers.set(channel, handler);
    });
    
    // 创建模拟窗口
    mockWindow = new BrowserWindow();
  });

  afterAll(async () => {
    // 清理测试数据
    try {
      await fs.unlink(TEST_DATA_PATH);
    } catch {
      // 忽略文件不存在的错误
    }
  });

  beforeEach(async () => {
    // 为每个测试创建新的服务实例
    speciesService = new SpeciesService(TEST_DATA_PATH);
    await speciesService.ensureLoaded();
    
    // 注册IPC处理器（模拟main.ts中的注册）
    registerSpeciesIpcHandlers(speciesService);
  });

  afterEach(() => {
    // 清理服务缓存
    if (speciesService) {
      speciesService.clearCache();
    }
  });

  describe('完整查询流程测试', () => {
    it('应该支持完整的搜索 -> 获取详情流程', async () => {
      // 1. 搜索物种
      const searchHandler = ipcHandlers.get('search-species');
      const searchResult = await searchHandler({}, {
        query: '白头',
        limit: 10
      });

      expect(searchResult.success).toBe(true);
      expect(searchResult.data.species.length).toBeGreaterThan(0);
      
      const foundSpecies = searchResult.data.species[0];
      expect(foundSpecies.种).toContain('白头');

      // 2. 获取物种详细信息
      const infoHandler = ipcHandlers.get('get-species-info');
      const infoResult = await infoHandler({}, foundSpecies.种);

      expect(infoResult.success).toBe(true);
      expect(infoResult.data).not.toBeNull();
      expect(infoResult.data.种).toBe(foundSpecies.种);
      expect(infoResult.data.科).toBe(foundSpecies.科);
      expect(infoResult.data.属).toBe(foundSpecies.属);
      expect(infoResult.data.目).toBe(foundSpecies.目);
    });

    it('应该支持搜索 -> 按科查询 -> 按属查询的完整流程', async () => {
      // 1. 搜索找到一个物种
      const searchHandler = ipcHandlers.get('search-species');
      const searchResult = await searchHandler({}, {
        query: '麻雀',
        limit: 5
      });

      expect(searchResult.success).toBe(true);
      expect(searchResult.data.species.length).toBeGreaterThan(0);
      
      const foundSpecies = searchResult.data.species[0];

      // 2. 按科查询相关物种
      const familyHandler = ipcHandlers.get('get-species-by-family');
      const familyResult = await familyHandler({}, foundSpecies.科, { limit: 10 });

      expect(familyResult.success).toBe(true);
      expect(familyResult.data.length).toBeGreaterThan(0);
      expect(familyResult.data.every((s: any) => s.科 === foundSpecies.科)).toBe(true);

      // 3. 按属查询更具体的物种
      const genusHandler = ipcHandlers.get('get-species-by-genus');
      const genusResult = await genusHandler({}, foundSpecies.属, { limit: 10 });

      expect(genusResult.success).toBe(true);
      expect(genusResult.data.length).toBeGreaterThan(0);
      expect(genusResult.data.every((s: any) => s.属 === foundSpecies.属)).toBe(true);
    });
  });

  describe('多种查询方式集成测试', () => {
    it('应该支持中文、拼音、首字母的混合查询', async () => {
      const searchHandler = ipcHandlers.get('search-species');

      // 中文查询
      const chineseResult = await searchHandler({}, { query: '鹎', limit: 10 });
      expect(chineseResult.success).toBe(true);
      const chineseCount = chineseResult.data.species.length;

      // 拼音查询 - 使用"bai"(白)而不是"bei"，因为测试数据中有"白头鹎"
      const pinyinResult = await searchHandler({}, { query: 'bai', limit: 10 });
      expect(pinyinResult.success).toBe(true);
      const pinyinCount = pinyinResult.data.species.length;

      // 首字母查询
      const initialResult = await searchHandler({}, { query: 'btb', limit: 10 });
      expect(initialResult.success).toBe(true);
      const initialCount = initialResult.data.species.length;

      // 验证不同查询方式都能找到相关结果
      expect(chineseCount).toBeGreaterThan(0);
      expect(pinyinCount).toBeGreaterThan(0);
      expect(initialCount).toBeGreaterThan(0);
    });

    it('应该支持模糊匹配和精确匹配的组合', async () => {
      const searchHandler = ipcHandlers.get('search-species');

      // 精确匹配
      const exactResult = await searchHandler({}, { query: '白头鹎', limit: 10 });
      expect(exactResult.success).toBe(true);
      expect(exactResult.data.species).toHaveLength(1);
      expect(exactResult.data.species[0].种).toBe('白头鹎');

      // 模糊匹配
      const fuzzyResult = await searchHandler({}, { query: '头', limit: 10 });
      expect(fuzzyResult.success).toBe(true);
      expect(fuzzyResult.data.species.length).toBeGreaterThan(1);
      expect(fuzzyResult.data.species.some((s: any) => s.种.includes('头'))).toBe(true);
    });
  });

  describe('数据一致性测试', () => {
    it('搜索结果和详情查询应该返回一致的数据', async () => {
      const searchHandler = ipcHandlers.get('search-species');
      const infoHandler = ipcHandlers.get('get-species-info');

      // 搜索所有物种
      const searchResult = await searchHandler({}, { query: '鸟', limit: 100 });
      expect(searchResult.success).toBe(true);

      // 对每个搜索结果验证详情查询的一致性
      for (const species of searchResult.data.species.slice(0, 3)) { // 测试前3个
        const infoResult = await infoHandler({}, species.种);
        
        expect(infoResult.success).toBe(true);
        expect(infoResult.data).not.toBeNull();
        expect(infoResult.data.种).toBe(species.种);
        expect(infoResult.data.科).toBe(species.科);
        expect(infoResult.data.属).toBe(species.属);
        expect(infoResult.data.目).toBe(species.目);
      }
    });

    it('按科查询和按属查询的结果应该符合分类层次', async () => {
      const familyHandler = ipcHandlers.get('get-species-by-family');
      const genusHandler = ipcHandlers.get('get-species-by-genus');

      // 按科查询
      const familyResult = await familyHandler({}, '麻雀科', { limit: 10 });
      expect(familyResult.success).toBe(true);
      expect(familyResult.data.length).toBeGreaterThan(0);

      // 验证科下的所有属
      const genera = [...new Set(familyResult.data.map((s: any) => s.属))];
      
      // 对每个属进行查询，验证结果是科查询结果的子集
      for (const genus of genera) {
        const genusResult = await genusHandler({}, genus, { limit: 10 });
        expect(genusResult.success).toBe(true);
        
        // 属查询的结果应该都在科查询结果中
        for (const genusSpecies of genusResult.data) {
          const foundInFamily = familyResult.data.some((familySpecies: any) => 
            familySpecies.种 === genusSpecies.种
          );
          expect(foundInFamily).toBe(true);
        }
      }
    });
  });

  describe('并发和压力测试', () => {
    it('应该支持多个并发查询请求', async () => {
      const searchHandler = ipcHandlers.get('search-species');
      
      // 创建多个并发查询
      const queries = ['白头', '麻雀', '鹰', '鸽', '隼'];
      const promises = queries.map(query => 
        searchHandler({}, { query, limit: 5 })
      );

      const results = await Promise.all(promises);

      // 验证所有查询都成功
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data.species).toBeDefined();
        console.log(`查询 "${queries[index]}" 返回 ${result.data.species.length} 条结果`);
      });
    });

    it('应该支持混合类型的并发查询', async () => {
      const searchHandler = ipcHandlers.get('search-species');
      const infoHandler = ipcHandlers.get('get-species-info');
      const familyHandler = ipcHandlers.get('get-species-by-family');

      // 混合不同类型的查询
      const promises = [
        searchHandler({}, { query: '白头', limit: 5 }),
        infoHandler({}, '家麻雀'),
        familyHandler({}, '鹰科', { limit: 5 }),
        searchHandler({}, { query: 'bei', limit: 3 }),
        infoHandler({}, '岩鸽'),
      ];

      const results = await Promise.all(promises);

      // 验证所有查询都成功
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('错误恢复测试', () => {
    it('应该能从服务错误中恢复', async () => {
      // 创建一个会失败的服务实例
      const faultyService = new SpeciesService('/invalid/path.json');
      registerSpeciesIpcHandlers(faultyService);

      const searchHandler = ipcHandlers.get('search-species');

      // 第一次查询应该失败
      const failResult = await searchHandler({}, { query: '测试', limit: 5 });
      expect(failResult.success).toBe(false);
      expect(failResult.error).toBeDefined();

      // 重新注册正常的服务（这会覆盖之前的处理器）
      registerSpeciesIpcHandlers(speciesService);

      // 获取新的处理器
      const newSearchHandler = ipcHandlers.get('search-species');

      // 第二次查询应该成功
      const successResult = await newSearchHandler({}, { query: '白头', limit: 5 });
      expect(successResult.success).toBe(true);
      expect(successResult.data.species.length).toBeGreaterThan(0);
    });
  });
});

/**
 * 注册物种查询IPC处理器
 */
function registerSpeciesIpcHandlers(service: SpeciesService) {
  // search-species
  ipcMain.handle('search-species', async (event, options: SpeciesDictionarySearchOptions) => {
    try {
      const result = await service.searchSpecies(options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // get-species-info
  ipcMain.handle('get-species-info', async (event, speciesName: string) => {
    try {
      if (!speciesName || typeof speciesName !== 'string' || speciesName.trim() === '') {
        throw new Error('物种名称不能为空');
      }
      const result = await service.getSpeciesInfo(speciesName.trim());
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // get-species-by-family
  ipcMain.handle('get-species-by-family', async (event, familyName: string, options?: any) => {
    try {
      if (!familyName || typeof familyName !== 'string' || familyName.trim() === '') {
        throw new Error('科名不能为空');
      }
      const result = await service.getSpeciesByFamily(familyName.trim(), options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // get-species-by-genus
  ipcMain.handle('get-species-by-genus', async (event, genusName: string, options?: any) => {
    try {
      if (!genusName || typeof genusName !== 'string' || genusName.trim() === '') {
        throw new Error('属名不能为空');
      }
      const result = await service.getSpeciesByGenus(genusName.trim(), options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });
}
