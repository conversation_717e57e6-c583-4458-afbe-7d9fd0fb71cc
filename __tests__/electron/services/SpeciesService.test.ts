import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import path from 'path';
import fs from 'fs/promises';
import { SpeciesService } from '../../../electron/services/SpeciesService';
import type { SpeciesDictionaryEntry, SpeciesDictionarySearchOptions } from '../../../schemas/speciesDictionary';

// 模拟数据路径
const TEST_DATA_PATH = path.join(__dirname, '../../fixtures/test-species.json');

// 测试用物种数据
const mockSpeciesData: SpeciesDictionaryEntry[] = [
  { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "白头鹎" },
  { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "黑头鹎" },
  { "目": "雀形目", "科": "鸦科", "属": "鹎属", "种": "红头鹎" },
  { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "家麻雀" },
  { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "山麻雀" },
  { "目": "鹰形目", "科": "鹰科", "属": "鹰属", "种": "苍鹰" },
];

describe('SpeciesService', () => {
  let speciesService: SpeciesService;

  beforeEach(async () => {
    // 创建测试数据文件
    await fs.writeFile(TEST_DATA_PATH, JSON.stringify(mockSpeciesData, null, 2));
    
    // 创建服务实例
    speciesService = new SpeciesService(TEST_DATA_PATH);
  });

  afterEach(async () => {
    // 清理测试数据
    try {
      await fs.unlink(TEST_DATA_PATH);
    } catch {
      // 忽略文件不存在的错误
    }
  });

  describe('数据加载', () => {
    it('应该能够加载JSON数据', async () => {
      const isLoaded = await speciesService.ensureLoaded();
      expect(isLoaded).toBe(true);
    });

    it('应该正确解析物种数据', async () => {
      await speciesService.ensureLoaded();
      const stats = speciesService.getStats();
      expect(stats.totalSpecies).toBe(6);
      expect(stats.totalFamilies).toBe(3); // 鸦科、麻雀科、鹰科
      expect(stats.totalGenera).toBe(4);   // 鸦属、鹎属、麻雀属、鹰属
    });

    it('数据加载失败时应该抛出错误', async () => {
      const invalidService = new SpeciesService('/invalid/path.json');
      await expect(invalidService.ensureLoaded()).rejects.toThrow();
    });
  });

  describe('精确查询', () => {
    beforeEach(async () => {
      await speciesService.ensureLoaded();
    });

    it('应该能够精确匹配物种名', async () => {
      const result = await speciesService.searchSpecies({ query: '白头鹎', limit: 10 });
      
      expect(result.species).toHaveLength(1);
      expect(result.species[0].种).toBe('白头鹎');
      expect(result.total).toBe(1);
      expect(result.searchTime).toBeLessThan(50); // 50ms以内
    });

    it('精确匹配应该优先返回', async () => {
      const result = await speciesService.searchSpecies({ query: '鹎', limit: 10 });
      
      // 应该有多个结果
      expect(result.species.length).toBeGreaterThan(1);
      // 但第一个结果应该是精确匹配的（如果存在）
    });
  });

  describe('模糊查询', () => {
    beforeEach(async () => {
      await speciesService.ensureLoaded();
    });

    it('应该支持前缀匹配', async () => {
      const result = await speciesService.searchSpecies({ query: '白', limit: 10 });
      
      expect(result.species.length).toBeGreaterThan(0);
      expect(result.species.some(s => s.种.includes('白'))).toBe(true);
    });

    it('应该支持部分字符匹配', async () => {
      const result = await speciesService.searchSpecies({ query: '头', limit: 10 });
      
      expect(result.species.length).toBeGreaterThan(0);
      expect(result.species.some(s => s.种.includes('头'))).toBe(true);
    });

    it('应该支持N-gram子字符串匹配', async () => {
      const result = await speciesService.searchSpecies({ query: '头鹎', limit: 10 });
      
      expect(result.species.length).toBeGreaterThan(0);
      expect(result.species.some(s => s.种.includes('头鹎'))).toBe(true);
    });
  });

  describe('拼音查询', () => {
    beforeEach(async () => {
      await speciesService.ensureLoaded();
    });

    it('应该支持拼音全拼查询', async () => {
      const result = await speciesService.searchSpecies({ query: 'baitou', limit: 10 });
      
      expect(result.species.length).toBeGreaterThan(0);
      expect(result.species.some(s => s.种.includes('白头'))).toBe(true);
    });

    it('应该支持拼音首字母查询', async () => {
      const result = await speciesService.searchSpecies({ query: 'btb', limit: 10 });
      
      expect(result.species.length).toBeGreaterThan(0);
      expect(result.species.some(s => s.种.includes('白头鹎'))).toBe(true);
    });

    it('应该支持拼音前缀查询', async () => {
      const result = await speciesService.searchSpecies({ query: 'bt', limit: 10 });
      
      expect(result.species.length).toBeGreaterThan(0);
    });
  });

  describe('按科属查询', () => {
    beforeEach(async () => {
      await speciesService.ensureLoaded();
    });

    it('应该能够按科查询', async () => {
      const result = await speciesService.getSpeciesByFamily('鸦科', { limit: 10 });
      
      expect(result.length).toBe(3); // 白头鹎、黑头鹎、红头鹎
      expect(result.every(s => s.科 === '鸦科')).toBe(true);
    });

    it('应该能够按属查询', async () => {
      const result = await speciesService.getSpeciesByGenus('麻雀属', { limit: 10 });
      
      expect(result.length).toBe(2); // 家麻雀、山麻雀
      expect(result.every(s => s.属 === '麻雀属')).toBe(true);
    });

    it('查询不存在的科属应该返回空数组', async () => {
      const result = await speciesService.getSpeciesByFamily('不存在科', { limit: 10 });
      expect(result).toEqual([]);
    });
  });

  describe('物种信息获取', () => {
    beforeEach(async () => {
      await speciesService.ensureLoaded();
    });

    it('应该能够获取物种完整信息', async () => {
      const info = await speciesService.getSpeciesInfo('白头鹎');

      expect(info).not.toBeNull();
      expect(info?.种).toBe('白头鹎');
      expect(info?.科).toBe('鸦科');
      expect(info?.属).toBe('鸦属');
      expect(info?.目).toBe('雀形目');
    });

    it('查询不存在的物种应该返回null', async () => {
      const info = await speciesService.getSpeciesInfo('不存在的鸟');
      expect(info).toBeNull();
    });
  });

  describe('性能测试', () => {
    it('首次加载时间应该在1000ms以内', async () => {
      const startTime = Date.now();
      await speciesService.ensureLoaded();
      const loadTime = Date.now() - startTime;

      expect(loadTime).toBeLessThan(1000);
    });

    it('查询响应时间应该在50ms以内', async () => {
      await speciesService.ensureLoaded();

      const startTime = Date.now();
      await speciesService.searchSpecies({ query: '白头', limit: 10 });
      const searchTime = Date.now() - startTime;

      expect(searchTime).toBeLessThan(50);
    });

    it('应该支持并发查询', async () => {
      await speciesService.ensureLoaded();

      const promises = Array(10).fill(0).map(() =>
        speciesService.searchSpecies({ query: Math.random().toString(), limit: 5 })
      );

      const results = await Promise.all(promises);
      expect(results).toHaveLength(10);
    });
  });

  describe('错误处理', () => {
    it('无效查询参数应该抛出错误', async () => {
      await speciesService.ensureLoaded();

      await expect(
        speciesService.searchSpecies({ query: '', limit: 10 })
      ).rejects.toThrow();
    });

    it('超出限制的limit参数应该被调整', async () => {
      await speciesService.ensureLoaded();

      const result = await speciesService.searchSpecies({ query: '鹎', limit: 1000 });
      expect(result.species.length).toBeLessThanOrEqual(100); // 最大限制
    });
  });

  describe('内存管理', () => {
    it('应该能够清理缓存', async () => {
      await speciesService.ensureLoaded();
      const statsBefore = speciesService.getStats();
      expect(statsBefore.loaded).toBe(true);

      speciesService.clearCache();
      const statsAfter = speciesService.getStats();
      expect(statsAfter.loaded).toBe(false);
    });
  });
});
